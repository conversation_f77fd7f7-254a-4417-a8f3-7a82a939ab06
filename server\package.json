{"name": "network-protocol-explorer-server", "version": "1.0.0", "description": "网络协议探险家后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-pcap-parser": "^0.3.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["express", "network", "protocol", "pcap", "osi"], "author": "Network Protocol Explorer Team", "license": "MIT"}