{"name": "network-protocol-explorer", "version": "1.0.0", "description": "网络协议探险家 - OSI/TCP-IP模型可视化和PCAP文件分析工具", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["network", "protocol", "osi", "tcp-ip", "pcap", "packet-analysis", "visualization"], "author": "Network Protocol Explorer Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}